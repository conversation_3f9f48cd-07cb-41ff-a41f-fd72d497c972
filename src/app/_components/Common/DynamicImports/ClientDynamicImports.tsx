'use client';

import { ClientCookieBanner } from '@/src/app/_components/Common/CookieBanner/ClientCookieBanner';
import dynamic from 'next/dynamic';
import React, { Suspense } from 'react';

// Dynamic imports with suspense for client components
const Header = dynamic(() => import('@/src/app/_components/Common/Header/HeaderWrapper'), {
  loading: () => <div className="h-16 animate-pulse bg-gray-100" />,
});

const Footer = dynamic(() => import('@/src/app/_components/Common/Footer/Footer'), {
  loading: () => <div className="h-24 animate-pulse bg-gray-100" />,
});

const HydrationErrorBoundary = dynamic(
  () => import('@/src/app/_components/Common/ErrorBoundary/HydrationErrorBoundary')
);

export const DynamicFAQ = dynamic(
  () => import('@/src/app/_components/Pages/Home/FAQ').then((mod) => mod.FAQ),
  {
    loading: () => <div className="relative z-30 h-48 animate-pulse rounded-3xl bg-white" />,
  }
);

export { DynamicDelayedSurveyMonkey } from './DelayedSurveyMonkeyWrapper';

interface ClientLayoutWrapperProps {
  children: React.ReactNode;
  logoPath: string;
}

export function ClientLayoutWrapper({ children, logoPath }: ClientLayoutWrapperProps) {
  return (
    <Suspense fallback={<div className="min-h-screen animate-pulse bg-gray-50" />}>
      <HydrationErrorBoundary>
        <Suspense fallback={<div className="h-16 animate-pulse bg-gray-100" />}>
          <Header logoPath={logoPath} />
        </Suspense>
        <main className="mx-auto pb-8 pt-[110px]" style={{ contentVisibility: 'auto' }}>
          <Suspense fallback={<div className="min-h-screen animate-pulse bg-gray-50" />}>
            <HydrationErrorBoundary>{children}</HydrationErrorBoundary>
          </Suspense>
        </main>
        <Suspense fallback={<div className="h-24 animate-pulse bg-gray-100" />}>
          <Footer />
        </Suspense>
        <ClientCookieBanner />
      </HydrationErrorBoundary>
    </Suspense>
  );
}
